import csv
import random

# Non-toxic Gujarati sentences
non_toxic_sentences = [
    "આજે હવામાન ખૂબ સરસ છે.",
    "મને ગુજરાતી ભાષા ખૂબ ગમે છે.",
    "આપણે સાથે મળીને કામ કરવું જોઈએ.",
    "શિક્ષણ એ જીવનનો આધાર છે.",
    "પરિવાર સાથે સમય વિતાવવો મહત્વપૂર્ણ છે.",
    "આજે મેં એક સરસ પુસ્તક વાંચ્યું.",
    "ગુજરાત એક સુંદર રાજ્ય છે.",
    "મિત્રતા એ જીવનનો અમૂલ્ય ખજાનો છે.",
    "આરોગ્ય એ સૌથી મોટી સંપત્તિ છે.",
    "કલા અને સંસ્કૃતિ આપણી ઓળખ છે.",
    "પ્રકૃતિનું સંરક્ષણ કરવું જરૂરી છે.",
    "મહેનત કરવાથી સફળતા મળે છે.",
    "દરેક વ્યક્તિ અનન્ય અને મહત્વપૂર્ણ છે.",
    "સત્ય અને અહિંસા મહાત્મા ગાંધીના સિદ્ધાંતો હતા.",
    "ભારતીય સંસ્કૃતિ વિવિધતામાં એકતા દર્શાવે છે.",
    "વિજ્ઞાન અને ટેકનોલોજી આપણા જીવનને સરળ બનાવે છે.",
    "સંગીત મનને શાંતિ આપે છે.",
    "યોગ અને ધ્યાન માનસિક સ્વાસ્થ્ય માટે ફાયદાકારક છે.",
    "પર્યાવરણ સંરક્ષણ આપણી જવાબદારી છે.",
    "શિક્ષક સમાજના આર્કિટેક્ટ છે.",
    "ખેતી આપણા દેશની રીઢ છે.",
    "સહકાર અને સહયોગથી મોટા કામ થાય છે.",
    "સમય એ અમૂલ્ય સંપત્તિ છે.",
    "આશા અને વિશ્વાસ જીવનને આગળ વધારે છે.",
    "દાન અને સેવા માનવતાના ગુણો છે.",
    "સ્વચ્છતા એ આરોગ્યનો આધાર છે.",
    "ભાષા એ વિચારોની અભિવ્યક્તિનું માધ્યમ છે.",
    "પરંપરા અને આધુનિકતાનો સંતુલન જરૂરી છે.",
    "ન્યાય અને સમાનતા લોકશાહીના સ્તંભો છે.",
    "કૃતજ્ઞતા એ મહાન ગુણ છે.",
    "આજે મેં એક સરસ ફિલ્મ જોઈ.",
    "બાળકોનું હાસ્ય ખૂબ મીઠું લાગે છે.",
    "ઉત્સવો આપણને એકસાથે લાવે છે.",
    "પુસ્તકો જ્ઞાનના ભંડાર છે.",
    "સૂર્યોદય અને સૂર્યાસ્ત પ્રકૃતિના અદ્ભુત દૃશ્યો છે.",
    "ખેલકૂદ શારીરિક અને માનસિક વિકાસ માટે જરૂરી છે.",
    "માતૃભાષા આપણી સાચી ઓળખ છે.",
    "સંસ્કાર અને મૂલ્યો બાળપણથી શીખવા જોઈએ.",
    "પ્રેમ અને કરુણા વિશ્વને સુંદર બનાવે છે.",
    "સાદગી અને નમ્રતા મહાન ગુણો છે.",
    "આજે હું ખુશ છું.",
    "મારા મિત્રો ખૂબ સારા છે.",
    "આ ખાવાનું ખૂબ સ્વાદિષ્ટ છે.",
    "આજે સરસ દિવસ છે.",
    "મને નવું શીખવાનું ગમે છે.",
    "પરિવાર સાથે રહેવાનું સુખ અલગ છે.",
    "મિત્રતામાં વિશ્વાસ મહત્વપૂર્ણ છે.",
    "સફળતા માટે મહેનત જરૂરી છે.",
    "આશા હંમેશા રાખવી જોઈએ.",
    "સત્ય બોલવું સારું છે.",
    "દરેકને સમાન અધિકાર છે.",
    "શાંતિ અને પ્રેમ ફેલાવવા જોઈએ.",
    "આજે મેં કંઈક નવું શીખ્યું.",
    "પ્રકૃતિ ખૂબ સુંદર છે.",
    "સંગીત સાંભળવાનું મને ગમે છે.",
    "વાંચન એ સારી આદત છે.",
    "સહાય કરવી એ માનવતા છે.",
    "સમય પર કામ કરવું જોઈએ.",
    "આરોગ્ય જાળવવું મહત્વપૂર્ણ છે.",
    "સકારાત્મક વિચાર રાખવા જોઈએ.",
    "દરેકનો આદર કરવો જોઈએ.",
    "જીવનમાં સંતુલન જરૂરી છે.",
    "મહેનત કરવાથી ફળ મળે છે.",
    "સાચા મિત્રો અમૂલ્ય છે.",
    "શિક્ષણ દરેકનો અધિકાર છે.",
    "પ્રેમ અને સમજણ જરૂરી છે.",
    "સંસ્કૃતિનું સંરક્ષણ કરવું જોઈએ.",
    "આજે હું ખુશ અને સંતુષ્ટ છું.",
    "જીવનમાં આનંદ શોધવો જોઈએ.",
    "દરેકની મદદ કરવી જોઈએ.",
    "સારા કામ કરવા જોઈએ.",
    "વિશ્વાસ અને પ્રેમ મહત્વપૂર્ણ છે.",
    "આજે સરસ અનુભવ થયો.",
    "નવા લોકોને મળવાનું સારું લાગે છે.",
    "જીવનમાં હંમેશા આશા રાખવી જોઈએ.",
    "સારા વિચારો રાખવા જોઈએ.",
    "દરેકને ખુશ રાખવાનો પ્રયાસ કરવો જોઈએ.",
    "સત્ય અને ન્યાયનો સાથ આપવો જોઈએ.",
    "જીવનમાં સંઘર્ષ કરવો પડે છે.",
    "મહેનત અને લગન જરૂરી છે.",
    "સારા સંસ્કાર બાળપણથી આપવા જોઈએ.",
    "પ્રકૃતિ સાથે તાલમેલ બેસાડવો જોઈએ.",
    "સમાજસેવા કરવી જોઈએ.",
    "દરેકની ભાવનાઓનો આદર કરવો જોઈએ.",
    "જીવનમાં સંતોષ રાખવો જોઈએ.",
    "સારા કામોમાં ભાગ લેવો જોઈએ.",
    "મિત્રતામાં વફાદારી જરૂરી છે.",
    "જીવનમાં સકારાત્મકતા લાવવી જોઈએ.",
    "દરેકને પ્રેમ અને આદર આપવો જોઈએ.",
    "સમયનો સદુપયોગ કરવો જોઈએ.",
    "જીવનમાં લક્ષ્ય રાખવું જરૂરી છે.",
    "સારા વિચારો અને કર્મો કરવા જોઈએ.",
    "દરેકની સાથે મિત્રતા કરવી જોઈએ.",
    "જીવનમાં આનંદ અને ખુશી શોધવી જોઈએ.",
    "સત્ય અને પ્રેમનો માર્ગ અપનાવવો જોઈએ.",
    "દરેકની મદદ કરવાનો પ્રયાસ કરવો જોઈએ.",
    "જીવનમાં સંતુલન અને શાંતિ જાળવવી જોઈએ.",
    "સારા કામો કરવાથી મન પ્રસન્ન રહે છે.",
    "દરેકને સમાન દૃષ્ટિએ જોવું જોઈએ.",
    "જીવનમાં હંમેશા આગળ વધવાનો પ્રયાસ કરવો જોઈએ.",
    "સારા મિત્રો અને પરિવાર જીવનનો આધાર છે.",
    "દરેકની ખુશીમાં ભાગ લેવો જોઈએ.",
    "જીવનમાં પ્રેમ અને કરુણા ફેલાવવી જોઈએ.",
    "સત્ય અને ન્યાયનો સાથ આપવાથી સમાજ સુધરે છે.",
    "દરેકને પોતાની ક્ષમતા પ્રમાણે કામ કરવું જોઈએ.",
    "જીવનમાં સંઘર્ષ કરીને આગળ વધવું જોઈએ.",
    "સારા વિચારો અને સારા કર્મો કરવાથી જીવન સફળ બને છે.",
    "દરેકની સાથે પ્રેમ અને સમજણથી વર્તવું જોઈએ.",
    "જીવનમાં હંમેશા સકારાત્મક રહેવાનો પ્રયાસ કરવો જોઈએ.",
    "સારા કામો કરવાથી સમાજમાં સારો બદલાવ આવે છે.",
    "દરેકને પોતાના સપના સાકાર કરવાનો અધિકાર છે.",
    "જીવનમાં પ્રેમ, શાંતિ અને આનંદ હોવો જોઈએ.",
    "સત્ય, અહિંસા અને પ્રેમનો માર્ગ અપનાવવાથી જીવન સુંદર બને છે."
]

# Toxic Gujarati sentences
toxic_sentences = [
    "તું બહુ મૂર્ખ છે.",
    "તારી વાત કોઈ સાંભળતું નથી.",
    "તું કંઈ કામનો નથી.",
    "તારાથી કશું થતું નથી.",
    "તું હંમેશા ખોટું બોલે છે.",
    "તારી પાસે કોઈ અક્કલ નથી.",
    "તું બહુ નકામો છે.",
    "તારાથી કોઈ કામ નથી થતું.",
    "તું હંમેશા ગડબડ કરે છે.",
    "તારી વાત કોઈ માનતું નથી.",
    "તું બહુ આળસુ છે.",
    "તારાથી કશું સારું નથી થતું.",
    "તું હંમેશા ખરાબ કામ કરે છે.",
    "તારી પાસે કોઈ ગુણ નથી.",
    "તું બહુ બેકાર છે.",
    "તારાથી કોઈને ફાયદો નથી.",
    "તું હંમેશા નુકસાન કરે છે.",
    "તારી વાત કોઈ સમજતું નથી.",
    "તું બહુ અજ્ઞાન છે.",
    "તારાથી કશું સારું કામ નથી થતું.",
    "તું હંમેશા ખોટા કામ કરે છે.",
    "તારી પાસે કોઈ સમજ નથી.",
    "તું બહુ નિકમ્મો છે.",
    "તારાથી કોઈ સારું કામ નથી થતું.",
    "તું હંમેશા બીજાને પરેશાન કરે છે.",
    "તારી વાત કોઈ ગંભીરતાથી લેતું નથી.",
    "તું બહુ અવિવેકી છે.",
    "તારાથી કશું યોગ્ય નથી થતું.",
    "તું હંમેશા ખરાબ વર્તન કરે છે.",
    "તારી પાસે કોઈ શિષ્ટાચાર નથી.",
    "તું બહુ અસભ્ય છે.",
    "તારાથી કોઈને ખુશી નથી મળતી.",
    "તું હંમેશા દુઃખ આપે છે.",
    "તારી વાત કોઈ યાદ રાખતું નથી.",
    "તું બહુ ભૂલકો છે.",
    "તારાથી કશું યાદ નથી રહેતું.",
    "તું હંમેશા ભૂલ કરે છે.",
    "તારી પાસે કોઈ યાદશક્તિ નથી.",
    "તું બહુ અવિશ્વસનીય છે.",
    "તારાથી કોઈ વિશ્વાસ નથી કરતું.",
    "તું હંમેશા વિશ્વાસઘાત કરે છે.",
    "તારી વાત કોઈ સાચી માનતું નથી.",
    "તું બહુ જૂઠો છે.",
    "તારાથી કશું સત્ય નથી બોલાતું.",
    "તું હંમેશા છેતરે છે.",
    "તારી પાસે કોઈ પ્રામાણિકતા નથી.",
    "તું બહુ કપટી છે.",
    "તારાથી કોઈને નુકસાન જ થાય છે.",
    "તું હંમેશા બીજાનું ખરાબ કરે છે.",
    "તારી વાત કોઈ સાંભળવા માંગતું નથી.",
    "તું બહુ કંટાળાજનક છે.",
    "તારાથી કોઈને આનંદ નથી આવતો.",
    "તું હંમેશા બોર કરે છે.",
    "તારી પાસે કોઈ રસપ્રદ વાત નથી.",
    "તું બહુ નિરસ છે.",
    "તારાથી કશું મજાનું નથી થતું.",
    "તું હંમેશા ઉદાસ કરે છે.",
    "તારી વાત કોઈ રસથી સાંભળતું નથી.",
    "તું બહુ નકારાત્મક છે.",
    "તારાથી કોઈને પ્રેરણા નથી મળતી.",
    "તું હંમેશા નિરાશ કરે છે.",
    "તારી પાસે કોઈ સકારાત્મકતા નથી.",
    "તું બહુ હતાશ છે.",
    "તારાથી કશું પ્રેરણાદાયક નથી થતું.",
    "તું હંમેશા બીજાને નીચે ખેંચે છે.",
    "તારી વાત કોઈ પ્રેરણાદાયક લાગતી નથી.",
    "તું બહુ નિષ્ક્રિય છે.",
    "તારાથી કોઈ સક્રિયતા નથી આવતી.",
    "તું હંમેશા આળસ કરે છે.",
    "તારી પાસે કોઈ ઉત્સાહ નથી.",
    "તું બહુ ઉદાસીન છે.",
    "તારાથી કશું ઉત્સાહજનક નથી થતું.",
    "તું હંમેશા બીજાને હતોત્સાહિત કરે છે.",
    "તારી વાત કોઈ ઉત્સાહથી સાંભળતું નથી.",
    "તું બહુ અસહાય છે.",
    "તારાથી કોઈને મદદ નથી મળતી.",
    "તું હંમેશા બીજા પર નિર્ભર રહે છે.",
    "તારી પાસે કોઈ સ્વતંત્રતા નથી.",
    "તું બહુ પરાધીન છે.",
    "તારાથી કશું સ્વતંત્ર નથી થતું.",
    "તું હંમેશા બીજાની મદદ માંગે છે.",
    "તારી વાત કોઈ સ્વતંત્ર લાગતી નથી.",
    "તું બહુ કમજોર છે.",
    "તારાથી કોઈ મજબૂત કામ નથી થતું.",
    "તું હંમેશા હાર માને છે.",
    "તારી પાસે કોઈ હિંમત નથી.",
    "તું બહુ ડરપોક છે.",
    "તારાથી કશું હિંમતવાળું નથી થતું.",
    "તું હંમેશા ભાગી જાય છે.",
    "તારી વાત કોઈ હિંમતવાળી લાગતી નથી.",
    "તું બહુ કાયર છે.",
    "તારાથી કોઈ બહાદુરીનું કામ નથી થતું.",
    "તું હંમેશા છુપાઈ જાય છે.",
    "તારી પાસે કોઈ બહાદુરી નથી.",
    "તું બહુ ભીરુ છે.",
    "તારાથી કશું બહાદુરીવાળું નથી થતું.",
    "તું હંમેશા બીજાની પાછળ છુપાય છે.",
    "તારી વાત કોઈ બહાદુરીવાળી લાગતી નથી.",
    "તું બહુ અયોગ્ય છે.",
    "તારાથી કોઈ યોગ્ય કામ નથી થતું.",
    "તું હંમેશા અયોગ્ય વર્તન કરે છે.",
    "તારી પાસે કોઈ યોગ્યતા નથી.",
    "તું બહુ અનુચિત છે.",
    "તારાથી કશું ઉચિત નથી થતું.",
    "તું હંમેશા અનુચિત કામ કરે છે.",
    "તારી વાત કોઈ ઉચિત લાગતી નથી.",
    "તું બહુ અશિષ્ટ છે.",
    "તારાથી કોઈ શિષ્ટ વર્તન નથી આવતું."
]

def generate_dataset():
    data = []

    # Add header
    data.append(['text', 'label'])

    # Generate 500 non-toxic samples
    for i in range(500):
        sentence = random.choice(non_toxic_sentences)
        data.append([sentence, 'non-toxic'])

    # Generate 500 toxic samples
    for i in range(500):
        sentence = random.choice(toxic_sentences)
        data.append([sentence, 'toxic'])

    # Shuffle the data (except header)
    header = data[0]
    rows = data[1:]
    random.shuffle(rows)
    data = [header] + rows

    return data

# Generate and save the dataset
dataset = generate_dataset()

with open('gujarati_toxicity_dataset.csv', 'w', newline='', encoding='utf-8') as file:
    writer = csv.writer(file)
    writer.writerows(dataset)

print(f"Dataset generated successfully with {len(dataset)-1} samples!")
print("Distribution:")
print("- Non-toxic samples: 500")
print("- Toxic samples: 500")
print("- Total samples: 1000")